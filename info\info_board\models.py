from django.db import models
from django.db.models.signals import pre_delete, post_delete, pre_save
from django.dispatch import receiver
import os


def validate_presentation_file(value):
    """Validate that the uploaded file is a PowerPoint or PDF file."""
    if value:
        ext = os.path.splitext(value.name)[1].lower()
        if ext not in ['.ppt', '.pptx', '.pdf']:
            from django.core.exceptions import ValidationError
            raise ValidationError(
                'Only PowerPoint (.ppt, .pptx) and PDF (.pdf) files are allowed.')


class Announcement(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    date_posted = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    background_image = models.ImageField(
        upload_to='announcements/', null=True, blank=True)
    attachment_file = models.FileField(
        upload_to='announcements/attachments/',
        null=True,
        blank=True,
        validators=[validate_presentation_file],
        help_text='Upload PowerPoint (.ppt, .pptx) or PDF (.pdf) files'
    )

    def __str__(self):
        return self.title

    def has_powerpoint_attachment(self):
        """Check if the announcement has a PowerPoint attachment."""
        if self.attachment_file:
            ext = os.path.splitext(self.attachment_file.name)[1].lower()
            return ext in ['.ppt', '.pptx']
        return False

    def has_pdf_attachment(self):
        """Check if the announcement has a PDF attachment."""
        if self.attachment_file:
            ext = os.path.splitext(self.attachment_file.name)[1].lower()
            return ext == '.pdf'
        return False

    def has_presentation_attachment(self):
        """Check if the announcement has any presentation attachment (PowerPoint or PDF)."""
        if self.attachment_file:
            ext = os.path.splitext(self.attachment_file.name)[1].lower()
            return ext in ['.ppt', '.pptx', '.pdf']
        return False

    def get_file_extension(self):
        """Get the file extension of the attachment."""
        if self.attachment_file:
            return os.path.splitext(self.attachment_file.name)[1].lower()
        return None

    def get_pdf_path(self):
        """Get the PDF version of the attachment (convert if PowerPoint, return as-is if PDF)."""
        if self.has_pdf_attachment():
            # Already a PDF, return the file path
            return self.attachment_file.name
        elif self.has_powerpoint_attachment():
            # PowerPoint file, convert to PDF
            from .utils import get_pdf_path_for_announcement
            return get_pdf_path_for_announcement(self)
        return None

    def get_pdf_url(self, request):
        """Get the full URL for the PDF version."""
        if self.has_pdf_attachment():
            # Already a PDF, return direct URL
            return request.build_absolute_uri(self.attachment_file.url)
        elif self.has_powerpoint_attachment():
            # PowerPoint file, get converted PDF URL
            from .utils import get_pdf_url_for_announcement
            return get_pdf_url_for_announcement(self, request)
        return None


class EmailMessage(models.Model):
    sender_email = models.EmailField()
    recipient_email = models.EmailField()
    content = models.TextField()


# Signal handlers for automatic file deletion
@receiver(pre_delete, sender=Announcement)
def delete_announcement_files(sender, instance, **kwargs):
    """
    Delete the associated files when an Announcement is deleted.
    This signal is triggered before the model instance is deleted from the database.
    """
    # Delete background image
    if instance.background_image:
        if os.path.isfile(instance.background_image.path):
            try:
                os.remove(instance.background_image.path)
                print(f"Deleted image file: {instance.background_image.path}")
            except OSError as e:
                print(
                    f"Error deleting image file {instance.background_image.path}: {e}")

    # Delete attachment file
    if instance.attachment_file:
        if os.path.isfile(instance.attachment_file.path):
            try:
                os.remove(instance.attachment_file.path)
                print(
                    f"Deleted attachment file: {instance.attachment_file.path}")
            except OSError as e:
                print(
                    f"Error deleting attachment file {instance.attachment_file.path}: {e}")

    # Always try to delete the corresponding PDF file (for PowerPoint conversions)
    try:
        from django.conf import settings

        # For PowerPoint files, delete the converted PDF
        if instance.has_powerpoint_attachment():
            from .utils import get_pdf_path_for_announcement
            pdf_path = get_pdf_path_for_announcement(instance)
            if pdf_path:
                full_pdf_path = os.path.join(settings.MEDIA_ROOT, pdf_path)
                if os.path.isfile(full_pdf_path):
                    os.remove(full_pdf_path)
                    print(f"Deleted converted PDF file: {full_pdf_path}")

        # Also check for any PDF files in the pdf directory with similar names
        if instance.attachment_file:
            base_name = os.path.splitext(
                os.path.basename(instance.attachment_file.name))[0]
            pdf_dir = os.path.join(settings.MEDIA_ROOT, 'announcements', 'pdf')
            if os.path.exists(pdf_dir):
                for filename in os.listdir(pdf_dir):
                    if filename.startswith(base_name) and filename.endswith('.pdf'):
                        pdf_file_path = os.path.join(pdf_dir, filename)
                        try:
                            os.remove(pdf_file_path)
                            print(f"Deleted related PDF file: {pdf_file_path}")
                        except OSError as e:
                            print(
                                f"Error deleting PDF file {pdf_file_path}: {e}")

    except Exception as e:
        print(f"Error during PDF cleanup: {e}")


@receiver(post_delete, sender=Announcement)
def cleanup_empty_directories(sender, instance, **kwargs):
    """
    Clean up empty directories after deleting an announcement.
    This helps keep the media directory organized.
    """
    from django.conf import settings

    # Clean up background image directory
    if instance.background_image:
        try:
            directory = os.path.dirname(instance.background_image.path)
            if os.path.exists(directory) and not os.listdir(directory):
                os.rmdir(directory)
                print(f"Removed empty directory: {directory}")
        except OSError as e:
            print(f"Error removing directory: {e}")

    # Clean up attachment directory
    if instance.attachment_file:
        try:
            directory = os.path.dirname(instance.attachment_file.path)
            if os.path.exists(directory) and not os.listdir(directory):
                os.rmdir(directory)
                print(f"Removed empty attachment directory: {directory}")
        except OSError as e:
            print(f"Error removing attachment directory: {e}")

    # Clean up PDF directory if empty
    try:
        pdf_dir = os.path.join(settings.MEDIA_ROOT, 'announcements', 'pdf')
        if os.path.exists(pdf_dir) and not os.listdir(pdf_dir):
            os.rmdir(pdf_dir)
            print(f"Removed empty PDF directory: {pdf_dir}")
    except OSError as e:
        print(f"Error removing PDF directory: {e}")


@receiver(pre_save, sender=Announcement)
def delete_old_files_on_update(sender, instance, **kwargs):
    """
    Delete the old files when an Announcement's files are updated.
    This prevents orphaned files when files are replaced.
    """
    if not instance.pk:
        # This is a new instance, no old files to delete
        return

    try:
        # Get the old instance from the database
        old_instance = Announcement.objects.get(pk=instance.pk)

        # Check if the image field has changed
        if old_instance.background_image and old_instance.background_image != instance.background_image:
            # Delete the old image file
            if os.path.isfile(old_instance.background_image.path):
                try:
                    os.remove(old_instance.background_image.path)
                    print(
                        f"Deleted old image file: {old_instance.background_image.path}")
                except OSError as e:
                    print(
                        f"Error deleting old image file {old_instance.background_image.path}: {e}")

        # Check if the attachment field has changed
        if old_instance.attachment_file and old_instance.attachment_file != instance.attachment_file:
            # Delete the old attachment file
            if os.path.isfile(old_instance.attachment_file.path):
                try:
                    os.remove(old_instance.attachment_file.path)
                    print(
                        f"Deleted old attachment file: {old_instance.attachment_file.path}")
                except OSError as e:
                    print(
                        f"Error deleting old attachment file {old_instance.attachment_file.path}: {e}")

            # Also delete the old PDF file if it was a PowerPoint conversion
            try:
                from django.conf import settings

                if old_instance.has_powerpoint_attachment():
                    from .utils import get_pdf_path_for_announcement
                    old_pdf_path = get_pdf_path_for_announcement(old_instance)
                    if old_pdf_path:
                        full_pdf_path = os.path.join(
                            settings.MEDIA_ROOT, old_pdf_path)
                        if os.path.isfile(full_pdf_path):
                            os.remove(full_pdf_path)
                            print(
                                f"Deleted old converted PDF file: {full_pdf_path}")

                # Also check for any related PDF files
                base_name = os.path.splitext(os.path.basename(
                    old_instance.attachment_file.name))[0]
                pdf_dir = os.path.join(
                    settings.MEDIA_ROOT, 'announcements', 'pdf')
                if os.path.exists(pdf_dir):
                    for filename in os.listdir(pdf_dir):
                        if filename.startswith(base_name) and filename.endswith('.pdf'):
                            pdf_file_path = os.path.join(pdf_dir, filename)
                            try:
                                os.remove(pdf_file_path)
                                print(
                                    f"Deleted old related PDF file: {pdf_file_path}")
                            except OSError as e:
                                print(
                                    f"Error deleting old PDF file {pdf_file_path}: {e}")

            except Exception as e:
                print(f"Error during old PDF cleanup: {e}")
    except Announcement.DoesNotExist:
        # Old instance doesn't exist, this is a new record
        pass

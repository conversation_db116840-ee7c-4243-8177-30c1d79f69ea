
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse
from .models import Announcement, EHS
from django.core.mail import send_mail
from .forms import EmailForm
from django.contrib import messages


def home(request):
    return render(request, 'info_board/home.html')


def announcement_list(request):
    announcements = Announcement.objects.filter(
        is_active=True).order_by('-date_posted')
    return render(request, 'info_board/announcement_list.html', {'announcements': announcements})


def ehs_list(request):
    ehs_items = EHS.objects.filter(
        is_active=True).order_by('-date_posted')
    return render(request, 'info_board/ehs_list.html', {'ehs_items': ehs_items})


def powerpoint_embed(request, announcement_id):
    """Display presentation file (PowerPoint or PDF) viewer."""
    announcement = get_object_or_404(
        Announcement, id=announcement_id, is_active=True)

    if not announcement.has_presentation_attachment():
        return HttpResponse("No presentation attachment found", status=404)

    # Get file information
    import os
    file_path = announcement.attachment_file.path
    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
    file_size_mb = round(file_size / (1024 * 1024), 2)
    file_name = os.path.basename(announcement.attachment_file.name)
    file_extension = announcement.get_file_extension()

    # Determine file type and get PDF URL
    if announcement.has_pdf_attachment():
        # Already a PDF file
        pdf_url = announcement.get_pdf_url(request)
        conversion_status = "pdf_original"
        file_type = "PDF"
    elif announcement.has_powerpoint_attachment():
        # PowerPoint file, convert to PDF
        pdf_url = announcement.get_pdf_url(request)
        conversion_status = "success" if pdf_url else "failed"
        file_type = "PowerPoint"
    else:
        conversion_status = "failed"
        pdf_url = None
        file_type = "Unknown"

    # Original file URL for download
    file_url = request.build_absolute_uri(announcement.attachment_file.url)

    return render(request, 'info_board/powerpoint_embed.html', {
        'announcement': announcement,
        'file_name': file_name,
        'file_size_mb': file_size_mb,
        'file_extension': file_extension,
        'file_url': file_url,
        'pdf_url': pdf_url,
        'conversion_status': conversion_status,
        'file_type': file_type
    })


def ehs_powerpoint_embed(request, ehs_id):
    """Display presentation file (PowerPoint or PDF) viewer for EHS."""
    ehs = get_object_or_404(EHS, id=ehs_id, is_active=True)

    if not ehs.has_presentation_attachment:
        return HttpResponse("No presentation attachment found", status=404)

    # Get file information
    import os
    file_path = ehs.attachment_file.path
    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
    file_size_mb = round(file_size / (1024 * 1024), 2)
    file_name = os.path.basename(ehs.attachment_file.name)
    file_extension = ehs.get_file_extension()

    # Determine file type and get PDF URL
    if ehs.has_pdf_attachment:
        # Already a PDF file
        pdf_url = ehs.get_pdf_url(request)
        conversion_status = "pdf_original"
        file_type = "PDF"
    elif ehs.has_powerpoint_attachment:
        # PowerPoint file, convert to PDF
        pdf_url = ehs.get_pdf_url(request)
        conversion_status = "success" if pdf_url else "failed"
        file_type = "PowerPoint"
    else:
        conversion_status = "failed"
        pdf_url = None
        file_type = "Unknown"

    # Original file URL for download
    file_url = request.build_absolute_uri(ehs.attachment_file.url)

    return render(request, 'info_board/ehs_powerpoint_embed.html', {
        'ehs': ehs,
        'file_name': file_name,
        'file_size_mb': file_size_mb,
        'file_extension': file_extension,
        'file_url': file_url,
        'pdf_url': pdf_url,
        'conversion_status': conversion_status,
        'file_type': file_type
    })


def send_email(request):
    if request.method == 'POST':
        form = EmailForm(request.POST)
        if form.is_valid():
            sender_name = form.cleaned_data['sender_name']
            content = f"Küldő neve: {sender_name}\n\n{form.cleaned_data['content']}"
            send_mail(
                'Információs portál',  # Statikus tárgy
                content,
                form.cleaned_data['sender_email'],
                [form.cleaned_data['recipient_email']],
                fail_silently=False,
            )
            messages.success(request, 'Email sikeresen elküldve!')
            return redirect('send_email')
    else:
        form = EmailForm()
    return render(request, 'info_board/email_form.html', {'form': form})
